<style>
</style>

<script type="text/x-template" id="adminTaskLog">

    <div>
        <el-form :inline="true" @keyup.native.enter="getData">
            <el-form-item label="id">
                <el-input v-model="queryForm.id" placeholder="仅示例，后台未实现"></el-input>
            </el-form-item>
            <el-input style="display: none"></el-input> <!-- hidden el-input to make keyup search work when there is only one input -->
            <el-form-item>
                <el-button type="primary" @click="(queryForm.page=1) && getData()">查询</el-button>
                <el-button @click="resetQuery">重置</el-button>
##                <el-button type="success" @click="handleAddOrEdit(true)">新增</el-button>
            </el-form-item>
        </el-form>

        <el-table :data="tableData" border stripe v-loading.body="tableLoading">
            <el-table-column prop="id" label="ID"></el-table-column>
            <el-table-column prop="createTime" label="创建时间"></el-table-column>
##            <el-table-column prop="updateTime" label="更新时间"></el-table-column>
            <el-table-column prop="costMs" label="耗时"></el-table-column>
##            <el-table-column prop="taskId" label="任务ID"></el-table-column>
##            <el-table-column prop="taskName" label="任务名称"></el-table-column>
##            <el-table-column prop="taskCode" label="任务编号"></el-table-column>
            <el-table-column prop="className" label="类名称"></el-table-column>
            <el-table-column prop="methodName" label="方法名称"></el-table-column>
            <el-table-column prop="args" label="任务参数"></el-table-column>
            <el-table-column prop="cronExpression" label="Cron表达式"></el-table-column>
            <el-table-column prop="fixRateMs" label="固定频率(ms)"></el-table-column>
            <el-table-column prop="triggerType" label="触发类型" width="100px">
                <template slot-scope="scope">
                    <span v-if="scope.row.triggerType === 'SCHEDULED'" style="color: blue">定时任务</span>
                    <span v-else-if="scope.row.triggerType === 'MANUAL'" style="color: green">页面触发</span>
                    <span v-else>{{scope.row.triggerType}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="status" label="执行状态" width="100px">
                <template slot-scope="scope">
                    <span v-if="scope.row.status === 'SUCCESS'" style="color: green">成功</span>
                    <span v-else-if="scope.row.status === 'FAIL'" style="color: red">失败</span>
                    <span v-else-if="scope.row.status === 'SKIPPED'" style="color: orange">已跳过</span>
                    <span v-else-if="scope.row.status === 'NEW'" style="color: blue">新建</span>
                    <span v-else>{{scope.row.status}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="runIp" label="运行IP"></el-table-column>
            <el-table-column prop="timeoutSecond" label="超时时间(秒)"></el-table-column>
            <el-table-column prop="errorMsg" label="相关信息"></el-table-column>
            <el-table-column label="操作">
                <template slot-scope="scope">
                    <el-button type="primary" size="small" @click="handleAddOrEdit(false, scope.row)">编辑</el-button>
                    <el-button type="success" size="small" @click="handleRerun(scope.row)">重跑</el-button>
                </template>
            </el-table-column>
        </el-table>

        <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
                       :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
        </el-pagination>

        <el-dialog :title="dialogTitle" :visible.sync="showDialog" top="10px" :close-on-click-modal="false" :append-to-body='true'> <!-- append-to-body修复弹框蒙版问题 -->
            <el-form :model="addEditForm" label-position="right" label-width="150px" :rules="rules" ref="addEditForm">
                <el-form-item label="耗时" prop="costMs">
                    <el-input v-model="addEditForm.costMs" placeholder="耗时(毫秒)"></el-input>
                </el-form-item>
                <el-form-item label="任务ID" prop="taskId">
                    <el-input v-model="addEditForm.taskId" placeholder="关联的任务id"></el-input>
                </el-form-item>
                <el-form-item label="任务名称" prop="taskName">
                    <el-input v-model="addEditForm.taskName" placeholder="任务名称"></el-input>
                </el-form-item>
                <el-form-item label="任务编号" prop="taskCode">
                    <el-input v-model="addEditForm.taskCode" placeholder="任务编号，任务的唯一标识，可以指定，没有指定时值为class_name.method_name"></el-input>
                </el-form-item>
                <el-form-item label="类名称" prop="className">
                    <el-input v-model="addEditForm.className" placeholder="执行的类的名称"></el-input>
                </el-form-item>
                <el-form-item label="方法名称" prop="methodName">
                    <el-input v-model="addEditForm.methodName" placeholder="执行的方法名称"></el-input>
                </el-form-item>
                <el-form-item label="任务参数" prop="args">
                    <el-input v-model="addEditForm.args" placeholder="任务参数"></el-input>
                </el-form-item>
                <el-form-item label="Cron表达式" prop="cronExpression">
                    <el-input v-model="addEditForm.cronExpression" placeholder="如果是定时任务有cron表达式，则记录这里"></el-input>
                </el-form-item>
                <el-form-item label="固定频率(ms)" prop="fixRateMs">
                    <el-input v-model="addEditForm.fixRateMs" placeholder="如果任务有固定的频率，fixed和delay都记录在这里"></el-input>
                </el-form-item>
                <el-form-item label="执行状态" prop="status">
                    <el-input v-model="addEditForm.status" placeholder="任务执行状态"></el-input>
                </el-form-item>
                <el-form-item label="运行IP" prop="runIp">
                    <el-input v-model="addEditForm.runIp" placeholder="运行的机器ip"></el-input>
                </el-form-item>
                <el-form-item label="超时时间" prop="timeoutSecond">
                    <el-input v-model="addEditForm.timeoutSecond" placeholder="任务执行超时时间（秒）"></el-input>
                </el-form-item>
                <el-form-item label="相关信息" prop="errorMsg">
                    <el-input v-model="addEditForm.errorMsg" placeholder="任务报错信息"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button type="danger" @click="handleDelete(addEditForm)" v-show="addEditForm.id">删除</el-button>
                <el-button @click="showDialog = false">取消</el-button>
                <el-button type="primary" @click="doAddOrEdit">确定</el-button>
            </div>
        </el-dialog>

    </div>

</script>

<script>
    Vue.component('admin-task-log', {
        template: '#adminTaskLog',
        data: function () {
            const defaultQueryForm = { page: 1, pageSize: 10 }
            const defaultAddForm = {}
            return {
                defaultQueryForm: defaultQueryForm,
                queryForm: Utils.copy(defaultQueryForm),
                addEditForm: Utils.copy(defaultAddForm),
                rules: {/*name: Form.notBlankValidator('名称不能为空')*/},
                total: 0, tableData: [], tableLoading: false,
                showDialog: false, dialogTitle: ''
            }
        },
        props: {
            taskId: {
                type: Number,
                default: null
            }
        },
        created: function() {
            this.getData()
        },
        methods: {
            getData: function() {
                var that = this
                that.tableLoading = true
                if (this.taskId) {
                    this.queryForm.taskId = this.taskId
                }
                Resource.get("${_contextPath_}/admin_task/log_get_page", this.queryForm, function(resp){
                    that.tableData = resp.data.data
                    that.total = resp.data.total
                    that.tableLoading = false
                })
            },
            pageChange: function(page) {
                this.queryForm.page = page
                this.getData()
            },
            resetQuery: function() {
                this.queryForm = Utils.copy(this.defaultQueryForm)
            },
            handleDelete: function(row) {
                var that = this
                Message.confirm("确定要删除吗?", function(){
                    Resource.post("${_contextPath_}/admin_task/log_delete", {id: row.id}, function(){
                        that.showDialog = false
                        Message.success("删除成功，列表已刷新")
                        that.getData()
                    })
                })
            },
            handleAddOrEdit: function(isAdd, row) {
                this.showDialog = true
                this.dialogTitle = isAdd ? '新增记录任务的执行情况表' : '编辑'
                Form.clearError(this, 'addEditForm')
                this.addEditForm = isAdd ? Utils.copy(defaultAddForm) : Utils.copy(row)
            },
            doAddOrEdit: function() {
                var that = this
                var isEdit =  this.addEditForm.id ? true : false
                Form.validate(this, 'addEditForm', function() {
                    Resource.post("${_contextPath_}/admin_task/log_add_or_update", that.addEditForm, function(resp){
                        Message.success(isEdit ? "修改成功" : "新增成功")
                        isEdit ? (that.showDialog = false) : that.addEditForm = Utils.copy(defaultAddForm)
                        that.getData()
                    })
                })
            },
            handleRerun: function(row) {
                var that = this
                Message.confirm("确定要重跑这个任务吗？", function(){
                    Resource.post("${_contextPath_}/admin_task/log_rerun", {id: row.id}, function(resp){
                        Message.success("任务重跑提交成功，列表已刷新")
                        that.getData()
                    })
                })
            }
        }
    })
</script>